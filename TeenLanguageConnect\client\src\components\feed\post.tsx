import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Heart, MessageCircle, Send, Bookmark, MoreHorizontal } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow } from "date-fns";

interface PostUser {
  id: string;
  username?: string;
  firstName: string;
  lastName: string;
  profileImageUrl?: string;
  country?: string;
  languagesLearning?: string[];
}

interface PostProps {
  post: {
    id: number;
    content?: string;
    imageUrl?: string;
    videoUrl?: string;
    likesCount: number;
    commentsCount: number;
    createdAt: string;
    user: PostUser;
    isLiked?: boolean;
  };
}

export default function Post({ post }: PostProps) {
  const [isLiked, setIsLiked] = useState(post.isLiked || false);
  const [likesCount, setLikesCount] = useState(post.likesCount);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const likeMutation = useMutation({
    mutationFn: async () => {
      if (isLiked) {
        await apiRequest("DELETE", `/api/posts/${post.id}/like`);
      } else {
        await apiRequest("POST", `/api/posts/${post.id}/like`);
      }
    },
    onSuccess: () => {
      setIsLiked(!isLiked);
      setLikesCount(prev => isLiked ? prev - 1 : prev + 1);
      queryClient.invalidateQueries({ queryKey: ["/api/posts/feed"] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to like post",
        variant: "destructive",
      });
    },
  });

  const handleLike = () => {
    likeMutation.mutate();
  };

  const timeAgo = formatDistanceToNow(new Date(post.createdAt), { addSuffix: true });

  return (
    <article className="bg-card border-b border-border">
      {/* Post Header */}
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-3">
          <img
            src={post.user.profileImageUrl || `https://ui-avatars.com/api/?name=${post.user.firstName}+${post.user.lastName}&background=6366f1&color=fff`}
            alt={`${post.user.firstName} ${post.user.lastName}`}
            className="w-10 h-10 rounded-full object-cover"
          />
          <div>
            <div className="flex items-center space-x-2">
              <span className="font-semibold text-sm">
                {post.user.username || `${post.user.firstName} ${post.user.lastName}`}
              </span>
              {post.user.country && (
                <span className="text-xs">{post.user.country}</span>
              )}
              {post.user.languagesLearning && post.user.languagesLearning.length > 0 && (
                <span className="language-badge-learning text-xs px-2 py-0.5 rounded-full">
                  Learning {post.user.languagesLearning[0]}
                </span>
              )}
            </div>
            <span className="text-xs text-muted-foreground">{timeAgo}</span>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="p-2 hover:bg-muted rounded-full transition-colors"
        >
          <MoreHorizontal className="w-4 h-4 text-muted-foreground" />
        </Button>
      </div>

      {/* Post Content */}
      {post.imageUrl && (
        <div className="relative">
          <img
            src={post.imageUrl}
            alt="Post content"
            className="w-full h-80 object-cover"
          />
          {post.videoUrl && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Button className="bg-white bg-opacity-90 rounded-full p-4 hover:bg-opacity-100 transition-all">
                <Send className="w-6 h-6 text-primary ml-1" />
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Post Actions */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLike}
              className="p-0 hover:scale-110 transition-transform"
              disabled={likeMutation.isPending}
            >
              <Heart
                className={`w-6 h-6 ${
                  isLiked ? "text-red-500 fill-current" : "text-muted-foreground"
                }`}
              />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="p-0 hover:scale-110 transition-transform"
            >
              <MessageCircle className="w-6 h-6 text-muted-foreground" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="p-0 hover:scale-110 transition-transform"
            >
              <Send className="w-6 h-6 text-muted-foreground" />
            </Button>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="p-0 hover:scale-110 transition-transform"
          >
            <Bookmark className="w-6 h-6 text-muted-foreground" />
          </Button>
        </div>

        {/* Post Stats */}
        <div className="mb-2">
          <span className="font-semibold text-sm">{likesCount} likes</span>
        </div>

        {/* Post Caption */}
        {post.content && (
          <div className="text-sm">
            <span className="font-semibold">
              {post.user.username || `${post.user.firstName} ${post.user.lastName}`}
            </span>
            <span className="ml-2">{post.content}</span>
          </div>
        )}

        {/* View Comments */}
        {post.commentsCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            className="text-sm text-muted-foreground mt-1 p-0 h-auto font-normal"
          >
            View all {post.commentsCount} comments
          </Button>
        )}
      </div>
    </article>
  );
}
