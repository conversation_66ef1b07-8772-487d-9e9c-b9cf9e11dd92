import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Shuffle, Globe } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export default function RandomChat() {
  const [selectedLanguage, setSelectedLanguage] = useState<string>("");
  const [isMatching, setIsMatching] = useState(false);
  const { toast } = useToast();

  const languages = [
    "English",
    "Spanish",
    "French",
    "German",
    "Italian",
    "Portuguese",
    "Japanese",
    "Korean",
    "Chinese",
    "Arabic",
    "Russian",
    "Dutch",
  ];

  const startRandomChatMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/random-chat/start", {
        language: selectedLanguage,
      });
      return response.json();
    },
    onSuccess: (data) => {
      setIsMatching(false);
      toast({
        title: "Match Found!",
        description: "Starting conversation...",
      });
      // TODO: Navigate to chat interface or open chat modal
    },
    onError: (error) => {
      setIsMatching(false);
      toast({
        title: "No Match Found",
        description: "Try again or select a different language",
        variant: "destructive",
      });
    },
  });

  const handleStartRandomChat = () => {
    if (!selectedLanguage) {
      toast({
        title: "Select Language",
        description: "Please select a language for practice",
        variant: "destructive",
      });
      return;
    }

    setIsMatching(true);
    startRandomChatMutation.mutate();

    // Simulate matching time (remove in real implementation)
    setTimeout(() => {
      if (Math.random() > 0.3) {
        startRandomChatMutation.mutate();
      } else {
        setIsMatching(false);
        toast({
          title: "No users available",
          description: "Try again later or join a group instead",
          variant: "destructive",
        });
      }
    }, 3000);
  };

  return (
    <div className="gradient-primary p-6 rounded-xl text-white text-center">
      <div className="flex items-center justify-center mb-4">
        <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
          {isMatching ? (
            <Shuffle className="w-6 h-6 animate-spin" />
          ) : (
            <Globe className="w-6 h-6" />
          )}
        </div>
      </div>

      <h3 className="font-bold text-lg mb-2">Random Language Chat</h3>
      <p className="text-sm opacity-90 mb-4">
        Connect with someone learning your target language!
      </p>

      <div className="space-y-3">
        <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
          <SelectTrigger className="w-full bg-white/10 border-white/20 text-white">
            <SelectValue placeholder="Select practice language" />
          </SelectTrigger>
          <SelectContent>
            {languages.map((language) => (
              <SelectItem key={language} value={language}>
                {language}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button
          onClick={handleStartRandomChat}
          disabled={isMatching || !selectedLanguage}
          className="w-full bg-white text-primary font-medium hover:bg-white/90 transition-colors"
        >
          {isMatching ? (
            <div className="flex items-center space-x-2">
              <Shuffle className="w-4 h-4 animate-spin" />
              <span>Finding Match...</span>
            </div>
          ) : (
            "Start Random Chat"
          )}
        </Button>
      </div>

      {isMatching && (
        <div className="mt-4 text-xs opacity-80">
          Searching for someone practicing {selectedLanguage}...
        </div>
      )}
    </div>
  );
}
