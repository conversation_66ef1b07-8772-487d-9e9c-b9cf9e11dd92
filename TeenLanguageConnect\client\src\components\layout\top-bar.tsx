import { But<PERSON> } from "@/components/ui/button";
import { Heart, Send } from "lucide-react";

export default function TopBar() {
  return (
    <header className="sticky top-0 z-50 bg-card border-b border-border px-4 py-3">
      <div className="flex items-center justify-between">
        <h1 className="text-xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
          LinguaVibe
        </h1>
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            className="p-2 rounded-full hover:bg-muted transition-colors"
          >
            <Heart className="w-5 h-5 text-muted-foreground" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="p-2 rounded-full hover:bg-muted transition-colors"
          >
            <Send className="w-5 h-5 text-muted-foreground" />
          </Button>
        </div>
      </div>
    </header>
  );
}
