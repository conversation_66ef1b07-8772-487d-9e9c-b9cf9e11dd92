import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Globe, Users, MessageSquare, Heart } from "lucide-react";

export default function Landing() {
  const handleLogin = () => {
    window.location.href = "/api/login";
  };

  return (
    <div className="mobile-container min-h-screen bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5">
      <div className="px-6 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="w-20 h-20 gradient-primary rounded-full mx-auto mb-6 flex items-center justify-center">
            <Globe className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-2">
            LinguaVibe
          </h1>
          <p className="text-muted-foreground text-lg">
            Connect with language learners worldwide
          </p>
        </div>

        {/* Features */}
        <div className="space-y-4 mb-12">
          <Card className="border-primary/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-card-foreground">Language Exchange</h3>
                  <p className="text-sm text-muted-foreground">
                    Practice with native speakers from around the world
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-secondary/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center">
                  <MessageSquare className="w-5 h-5 text-secondary" />
                </div>
                <div>
                  <h3 className="font-semibold text-card-foreground">Group Conversations</h3>
                  <p className="text-sm text-muted-foreground">
                    Join voice groups and practice speaking confidently
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-accent/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
                  <Heart className="w-5 h-5 text-accent" />
                </div>
                <div>
                  <h3 className="font-semibold text-card-foreground">Social Learning</h3>
                  <p className="text-sm text-muted-foreground">
                    Share your progress and get motivated by others
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Benefits */}
        <div className="text-center mb-12">
          <h2 className="text-xl font-bold text-card-foreground mb-4">
            Perfect for Teenagers
          </h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="text-left">
              <div className="text-primary font-semibold">✓</div>
              <span className="text-muted-foreground">Safe & Secure</span>
            </div>
            <div className="text-left">
              <div className="text-primary font-semibold">✓</div>
              <span className="text-muted-foreground">Age-Appropriate</span>
            </div>
            <div className="text-left">
              <div className="text-primary font-semibold">✓</div>
              <span className="text-muted-foreground">Fun & Interactive</span>
            </div>
            <div className="text-left">
              <div className="text-primary font-semibold">✓</div>
              <span className="text-muted-foreground">Cultural Exchange</span>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="space-y-4">
          <Button 
            onClick={handleLogin}
            className="w-full gradient-primary text-white font-semibold py-6 text-lg btn-hover-scale"
          >
            Get Started - It's Free!
          </Button>
          
          <p className="text-xs text-muted-foreground text-center">
            Join thousands of teenagers learning languages together
          </p>
        </div>
      </div>
    </div>
  );
}
