import { useParams } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import TopBar from "@/components/layout/top-bar";
import BottomNav from "@/components/layout/bottom-nav";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Settings, Share, Edit } from "lucide-react";
import { useLocation } from "wouter";

export default function Profile() {
  const { userId } = useParams();
  const { user: currentUser } = useAuth();
  const [, setLocation] = useLocation();

  const profileUserId = userId || currentUser?.id;
  const isOwnProfile = !userId || userId === currentUser?.id;

  const { data: profileUser, isLoading } = useQuery({
    queryKey: ["/api/users/profile", profileUserId],
    enabled: !!profileUserId,
  });

  const { data: userPosts, isLoading: postsLoading } = useQuery({
    queryKey: ["/api/posts/user", profileUserId],
    enabled: !!profileUserId,
  });

  const handleBack = () => {
    setLocation("/");
  };

  if (isLoading) {
    return (
      <div className="mobile-container">
        <div className="p-4 space-y-4">
          <div className="flex items-center justify-between">
            <ArrowLeft className="w-6 h-6" />
            <Settings className="w-6 h-6" />
          </div>
          <div className="text-center space-y-4">
            <div className="w-24 h-24 rounded-full bg-muted mx-auto animate-pulse"></div>
            <div className="w-32 h-6 bg-muted rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-4 bg-muted rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  const user = profileUser || currentUser;

  if (!user) {
    return (
      <div className="mobile-container flex items-center justify-center">
        <p className="text-muted-foreground">User not found</p>
      </div>
    );
  }

  return (
    <div className="mobile-container">
      {/* Profile Header */}
      <header className="sticky top-0 bg-card border-b border-border px-4 py-3 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="p-2 hover:bg-muted rounded-full"
            >
              <ArrowLeft className="w-5 h-5 text-muted-foreground" />
            </Button>
            <span className="font-semibold">
              {user.username || `${user.firstName} ${user.lastName}`}
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="p-2 hover:bg-muted rounded-full"
          >
            <Settings className="w-5 h-5 text-muted-foreground" />
          </Button>
        </div>
      </header>

      <main className="pb-20">
        {/* Profile Info */}
        <div className="p-4">
          <div className="text-center mb-6">
            <img
              src={user.profileImageUrl || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=6366f1&color=fff`}
              alt="Profile"
              className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
            />
            <h2 className="text-xl font-bold text-card-foreground mb-1">
              {user.firstName} {user.lastName}
            </h2>
            <p className="text-muted-foreground mb-2">
              {user.country && <span>{user.country} </span>}
              {user.status && <span>• {user.status}</span>}
              {user.age && <span> • Age {user.age}</span>}
            </p>

            {/* Language Tags */}
            <div className="flex flex-wrap justify-center gap-2 mb-4">
              {user.languagesSpoken?.map((lang: string) => (
                <span key={lang} className="language-badge-native text-xs px-3 py-1 rounded-full">
                  Native: {lang}
                </span>
              ))}
              {user.languagesLearning?.map((lang: string) => (
                <span key={lang} className="language-badge-learning text-xs px-3 py-1 rounded-full">
                  Learning: {lang}
                </span>
              ))}
            </div>

            {/* Bio */}
            {user.bio && (
              <p className="text-sm text-muted-foreground max-w-xs mx-auto mb-4">
                {user.bio}
              </p>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3 justify-center">
              {isOwnProfile ? (
                <>
                  <Button className="gradient-primary text-white px-6 py-2 rounded-lg font-medium btn-hover-scale">
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Profile
                  </Button>
                  <Button variant="outline" className="px-6 py-2 rounded-lg font-medium">
                    <Share className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                </>
              ) : (
                <>
                  <Button className="gradient-primary text-white px-6 py-2 rounded-lg font-medium btn-hover-scale">
                    Follow
                  </Button>
                  <Button variant="outline" className="px-6 py-2 rounded-lg font-medium">
                    Message
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <div className="text-xl font-bold text-card-foreground">
                {userPosts?.length || 0}
              </div>
              <div className="text-sm text-muted-foreground">Posts</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-card-foreground">156</div>
              <div className="text-sm text-muted-foreground">Followers</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-card-foreground">89</div>
              <div className="text-sm text-muted-foreground">Following</div>
            </div>
          </div>

          {/* Interests */}
          {user.interests && user.interests.length > 0 && (
            <div className="mb-6">
              <h3 className="font-semibold text-card-foreground mb-2">Interests</h3>
              <div className="flex flex-wrap gap-2">
                {user.interests.map((interest: string) => (
                  <span
                    key={interest}
                    className="bg-muted text-muted-foreground text-xs px-2 py-1 rounded-full"
                  >
                    {interest}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Posts Grid */}
          <div className="posts-grid">
            {postsLoading ? (
              [...Array(9)].map((_, i) => (
                <div key={i} className="post-grid-item bg-muted animate-pulse"></div>
              ))
            ) : userPosts && userPosts.length > 0 ? (
              userPosts.map((post: any) => (
                <div key={post.id} className="post-grid-item bg-muted overflow-hidden">
                  {post.imageUrl && (
                    <img
                      src={post.imageUrl}
                      alt="Post"
                      className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                    />
                  )}
                </div>
              ))
            ) : (
              <div className="col-span-3 text-center py-12">
                <div className="w-16 h-16 bg-muted rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Edit className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold text-card-foreground mb-2">
                  No posts yet
                </h3>
                <p className="text-muted-foreground">
                  {isOwnProfile
                    ? "Share your language learning journey!"
                    : "This user hasn't shared any posts yet"}
                </p>
              </div>
            )}
          </div>
        </div>
      </main>

      <BottomNav />
    </div>
  );
}
