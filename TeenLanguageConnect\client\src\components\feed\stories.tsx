import { Plus } from "lucide-react";

interface Story {
  id: string;
  userId: string;
  user: {
    username: string;
    profileImageUrl?: string;
    firstName: string;
    lastName: string;
  };
  imageUrl: string;
  createdAt: string;
}

interface StoriesProps {
  stories: Story[];
}

export default function Stories({ stories }: StoriesProps) {
  return (
    <div className="flex space-x-4 overflow-x-auto scrollbar-hide">
      {/* Add Your Story */}
      <div className="flex flex-col items-center space-y-1 flex-shrink-0">
        <div className="relative">
          <div className="w-16 h-16 rounded-full gradient-primary p-0.5">
            <div className="w-full h-full rounded-full bg-card flex items-center justify-center">
              <Plus className="w-5 h-5 text-primary" />
            </div>
          </div>
        </div>
        <span className="text-xs text-muted-foreground">Your Story</span>
      </div>

      {/* Friends' Stories */}
      {stories.map((story) => (
        <div key={story.id} className="flex flex-col items-center space-y-1 flex-shrink-0">
          <div className="story-ring">
            <div className="story-ring-inner">
              <img
                src={story.user.profileImageUrl || `https://ui-avatars.com/api/?name=${story.user.firstName}+${story.user.lastName}&background=6366f1&color=fff`}
                alt={story.user.username}
                className="w-14 h-14 rounded-full object-cover"
              />
            </div>
          </div>
          <span className="text-xs text-muted-foreground max-w-[60px] truncate">
            {story.user.username || story.user.firstName}
          </span>
        </div>
      ))}
    </div>
  );
}
