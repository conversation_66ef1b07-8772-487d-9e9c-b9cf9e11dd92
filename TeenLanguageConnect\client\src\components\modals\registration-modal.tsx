import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Camera, Globe } from "lucide-react";
import { z } from "zod";

const registrationSchema = z.object({
  phoneNumber: z.string().min(10, "Phone number must be at least 10 digits"),
  countryCode: z.string().min(1, "Please select country code"),
  country: z.string().min(1, "Please select your country"),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  username: z.string().min(3, "Username must be at least 3 characters"),
  age: z.number().min(13).max(19, "Age must be between 13-19"),
  languagesSpoken: z.array(z.string()).min(1, "At least one language required"),
  languagesLearning: z.array(z.string()).min(1, "At least one language required"),
  interests: z.array(z.string()).optional(),
  status: z.string().min(1, "Please select your status"),
  bio: z.string().optional(),
});

type RegistrationForm = z.infer<typeof registrationSchema>;

interface RegistrationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function RegistrationModal({ open, onOpenChange }: RegistrationModalProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [step, setStep] = useState(1);
  const [spokenLanguages, setSpokenLanguages] = useState<string[]>([]);
  const [learningLanguages, setLearningLanguages] = useState<string[]>([]);
  const [userInterests, setUserInterests] = useState<string[]>([]);

  const form = useForm<RegistrationForm>({
    resolver: zodResolver(registrationSchema),
    defaultValues: {
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      languagesSpoken: [],
      languagesLearning: [],
      interests: [],
    },
  });

  const countries = [
    { code: "+1", name: "United States", flag: "🇺🇸" },
    { code: "+81", name: "Japan", flag: "🇯🇵" },
    { code: "+82", name: "South Korea", flag: "🇰🇷" },
    { code: "+34", name: "Spain", flag: "🇪🇸" },
    { code: "+33", name: "France", flag: "🇫🇷" },
    { code: "+49", name: "Germany", flag: "🇩🇪" },
    { code: "+86", name: "China", flag: "🇨🇳" },
    { code: "+55", name: "Brazil", flag: "🇧🇷" },
  ];

  const languages = [
    "English", "Spanish", "French", "German", "Italian", "Portuguese",
    "Japanese", "Korean", "Chinese", "Arabic", "Russian", "Dutch",
    "Hindi", "Thai", "Vietnamese", "Swedish", "Norwegian", "Finnish"
  ];

  const interests = [
    "Anime", "Music", "Travel", "Movies", "Gaming", "Sports", "Art",
    "Photography", "Cooking", "Reading", "Technology", "Fashion",
    "Dance", "Fitness", "Nature", "Science", "History", "Culture"
  ];

  const statuses = [
    "Student", "Working", "Learning", "Teaching", "Traveling"
  ];

  const updateMutation = useMutation({
    mutationFn: async (data: RegistrationForm) => {
      return await apiRequest("PUT", "/api/users/profile", {
        ...data,
        languagesSpoken: spokenLanguages,
        languagesLearning: learningLanguages,
        interests: userInterests,
      });
    },
    onSuccess: () => {
      toast({
        title: "Profile Updated!",
        description: "Welcome to LinguaVibe! Start connecting with language learners.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/auth/user"] });
      onOpenChange(false);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: RegistrationForm) => {
    updateMutation.mutate(data);
  };

  const addLanguage = (language: string, type: 'spoken' | 'learning') => {
    if (type === 'spoken' && !spokenLanguages.includes(language)) {
      setSpokenLanguages([...spokenLanguages, language]);
    } else if (type === 'learning' && !learningLanguages.includes(language)) {
      setLearningLanguages([...learningLanguages, language]);
    }
  };

  const removeLanguage = (language: string, type: 'spoken' | 'learning') => {
    if (type === 'spoken') {
      setSpokenLanguages(spokenLanguages.filter(l => l !== language));
    } else {
      setLearningLanguages(learningLanguages.filter(l => l !== language));
    }
  };

  const addInterest = (interest: string) => {
    if (!userInterests.includes(interest)) {
      setUserInterests([...userInterests, interest]);
    }
  };

  const removeInterest = (interest: string) => {
    setUserInterests(userInterests.filter(i => i !== interest));
  };

  const nextStep = () => setStep(step + 1);
  const prevStep = () => setStep(step - 1);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center">
            <div className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-2">
              Complete Your Profile
            </div>
            <p className="text-sm text-muted-foreground font-normal">
              Step {step} of 3 • Tell us about yourself
            </p>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            
            {/* Step 1: Basic Info */}
            {step === 1 && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name="countryCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country Code</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {countries.map((country) => (
                              <SelectItem key={country.code} value={country.code}>
                                {country.flag} {country.code}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phoneNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input placeholder="(*************" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Country</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select your country" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {countries.map((country) => (
                            <SelectItem key={country.name} value={country.name}>
                              {country.flag} {country.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Your first name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Your last name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Username</FormLabel>
                        <FormControl>
                          <Input placeholder="@username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="age"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Age</FormLabel>
                        <Select onValueChange={(value) => field.onChange(parseInt(value))}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Age" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Array.from({ length: 7 }, (_, i) => i + 13).map((age) => (
                              <SelectItem key={age} value={age.toString()}>
                                {age}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select your status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {statuses.map((status) => (
                            <SelectItem key={status} value={status}>
                              {status}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="button" onClick={nextStep} className="w-full gradient-primary text-white">
                  Continue
                </Button>
              </div>
            )}

            {/* Step 2: Languages */}
            {step === 2 && (
              <div className="space-y-4">
                <div>
                  <FormLabel>Languages You Speak</FormLabel>
                  <div className="flex flex-wrap gap-2 mt-2 mb-3">
                    {spokenLanguages.map((lang) => (
                      <span
                        key={lang}
                        onClick={() => removeLanguage(lang, 'spoken')}
                        className="language-badge-native text-xs px-2 py-1 rounded-full cursor-pointer"
                      >
                        {lang} ×
                      </span>
                    ))}
                  </div>
                  <Select onValueChange={(value) => addLanguage(value, 'spoken')}>
                    <SelectTrigger>
                      <SelectValue placeholder="Add a language you speak" />
                    </SelectTrigger>
                    <SelectContent>
                      {languages.filter(lang => !spokenLanguages.includes(lang)).map((lang) => (
                        <SelectItem key={lang} value={lang}>
                          {lang}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <FormLabel>Languages You Want to Learn</FormLabel>
                  <div className="flex flex-wrap gap-2 mt-2 mb-3">
                    {learningLanguages.map((lang) => (
                      <span
                        key={lang}
                        onClick={() => removeLanguage(lang, 'learning')}
                        className="language-badge-learning text-xs px-2 py-1 rounded-full cursor-pointer"
                      >
                        {lang} ×
                      </span>
                    ))}
                  </div>
                  <Select onValueChange={(value) => addLanguage(value, 'learning')}>
                    <SelectTrigger>
                      <SelectValue placeholder="Add a language to learn" />
                    </SelectTrigger>
                    <SelectContent>
                      {languages.filter(lang => !learningLanguages.includes(lang)).map((lang) => (
                        <SelectItem key={lang} value={lang}>
                          {lang}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex space-x-3">
                  <Button type="button" onClick={prevStep} variant="outline" className="flex-1">
                    Back
                  </Button>
                  <Button 
                    type="button" 
                    onClick={nextStep} 
                    className="flex-1 gradient-primary text-white"
                    disabled={spokenLanguages.length === 0 || learningLanguages.length === 0}
                  >
                    Continue
                  </Button>
                </div>
              </div>
            )}

            {/* Step 3: Interests & Bio */}
            {step === 3 && (
              <div className="space-y-4">
                <div>
                  <FormLabel>Interests (Optional)</FormLabel>
                  <div className="flex flex-wrap gap-2 mt-2 mb-3">
                    {userInterests.map((interest) => (
                      <span
                        key={interest}
                        onClick={() => removeInterest(interest)}
                        className="bg-muted text-muted-foreground text-xs px-2 py-1 rounded-full cursor-pointer"
                      >
                        {interest} ×
                      </span>
                    ))}
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    {interests.filter(interest => !userInterests.includes(interest)).slice(0, 12).map((interest) => (
                      <Button
                        key={interest}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addInterest(interest)}
                        className="text-xs h-8"
                      >
                        {interest}
                      </Button>
                    ))}
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="bio"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bio (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Tell others about yourself and your language learning goals..."
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="text-center">
                  <Button type="button" variant="outline" className="mb-2">
                    <Camera className="w-4 h-4 mr-2" />
                    Add Profile Photo (Optional)
                  </Button>
                  <p className="text-xs text-muted-foreground">You can skip this and add later</p>
                </div>

                <div className="flex space-x-3">
                  <Button type="button" onClick={prevStep} variant="outline" className="flex-1">
                    Back
                  </Button>
                  <Button 
                    type="submit" 
                    className="flex-1 gradient-primary text-white"
                    disabled={updateMutation.isPending}
                  >
                    {updateMutation.isPending ? "Saving..." : "Complete Setup"}
                  </Button>
                </div>
              </div>
            )}
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
