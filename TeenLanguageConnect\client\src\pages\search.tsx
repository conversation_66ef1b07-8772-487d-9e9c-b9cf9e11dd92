import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import TopBar from "@/components/layout/top-bar";
import BottomNav from "@/components/layout/bottom-nav";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Search as SearchIcon } from "lucide-react";
import { useLocation } from "wouter";

export default function Search() {
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchType, setSearchType] = useState<"posts" | "users">("posts");

  const { data: explorePosts, isLoading: exploreLoading } = useQuery({
    queryKey: ["/api/posts/explore"],
  });

  const { data: searchResults, isLoading: searchLoading } = useQuery({
    queryKey: ["/api/users/search", { q: searchQuery }],
    enabled: searchQuery.length > 2 && searchType === "users",
  });

  const handleBack = () => {
    setLocation("/");
  };

  return (
    <div className="mobile-container">
      {/* Search Header */}
      <header className="sticky top-0 bg-card border-b border-border px-4 py-3 z-10">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost" 
            size="sm"
            onClick={handleBack}
            className="p-2 hover:bg-muted rounded-full"
          >
            <ArrowLeft className="w-5 h-5 text-muted-foreground" />
          </Button>
          <div className="flex-1 relative">
            <Input
              type="text"
              placeholder="Search users, languages, interests..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-muted border-0 rounded-full px-4 py-2 pl-10 text-sm focus:ring-2 focus:ring-primary"
            />
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          </div>
        </div>

        {/* Search Type Toggle */}
        <div className="flex mt-3 bg-muted rounded-full p-1">
          <Button
            variant={searchType === "posts" ? "default" : "ghost"}
            size="sm"
            onClick={() => setSearchType("posts")}
            className={`flex-1 rounded-full text-sm ${
              searchType === "posts" ? "bg-card shadow-sm" : ""
            }`}
          >
            Posts
          </Button>
          <Button
            variant={searchType === "users" ? "default" : "ghost"}
            size="sm"
            onClick={() => setSearchType("users")}
            className={`flex-1 rounded-full text-sm ${
              searchType === "users" ? "bg-card shadow-sm" : ""
            }`}
          >
            Users
          </Button>
        </div>
      </header>

      <main className="pb-20">
        {/* Recent Searches */}
        {!searchQuery && (
          <div className="p-4">
            <h3 className="font-semibold text-card-foreground mb-3">Recent</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <SearchIcon className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground">Japanese conversation</span>
              </div>
              <div className="flex items-center space-x-3">
                <SearchIcon className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground">Spanish beginners</span>
              </div>
            </div>
          </div>
        )}

        {/* Search Results - Users */}
        {searchQuery && searchType === "users" && (
          <div className="p-4">
            <h3 className="font-semibold text-card-foreground mb-3">
              Users ({searchResults?.length || 0})
            </h3>
            {searchLoading ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <div className="w-12 h-12 rounded-full bg-muted animate-pulse"></div>
                    <div className="flex-1 space-y-2">
                      <div className="w-24 h-3 bg-muted rounded animate-pulse"></div>
                      <div className="w-32 h-2 bg-muted rounded animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : searchResults?.length > 0 ? (
              <div className="space-y-3">
                {searchResults.map((user: any) => (
                  <div key={user.id} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-muted/50">
                    <img
                      src={user.profileImageUrl || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=6366f1&color=fff`}
                      alt={`${user.firstName} ${user.lastName}`}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-semibold text-sm">
                          {user.username || `${user.firstName} ${user.lastName}`}
                        </span>
                        {user.country && (
                          <span className="text-xs">{user.country}</span>
                        )}
                      </div>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {user.languagesLearning?.slice(0, 2).map((lang: string) => (
                          <span key={lang} className="language-badge-learning text-xs px-2 py-0.5 rounded-full">
                            Learning {lang}
                          </span>
                        ))}
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      Follow
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No users found</p>
              </div>
            )}
          </div>
        )}

        {/* Explore Grid - Posts */}
        {(!searchQuery || searchType === "posts") && (
          <div className="p-1">
            {exploreLoading ? (
              <div className="posts-grid">
                {[...Array(12)].map((_, i) => (
                  <div key={i} className="post-grid-item bg-muted animate-pulse"></div>
                ))}
              </div>
            ) : explorePosts?.length > 0 ? (
              <div className="posts-grid">
                {explorePosts.map((post: any) => (
                  <div key={post.id} className="post-grid-item bg-muted overflow-hidden">
                    {post.imageUrl && (
                      <img
                        src={post.imageUrl}
                        alt="Post"
                        className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                      />
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-muted rounded-full mx-auto mb-4 flex items-center justify-center">
                  <SearchIcon className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold text-card-foreground mb-2">
                  Discover Content
                </h3>
                <p className="text-muted-foreground">
                  Explore posts from language learners around the world
                </p>
              </div>
            )}
          </div>
        )}
      </main>

      <BottomNav />
    </div>
  );
}
