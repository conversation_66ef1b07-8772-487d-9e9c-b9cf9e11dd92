# LinguaVibe - Language Exchange Social Platform

## Overview

LinguaVibe is a social media platform designed specifically for teenagers (13-19 years old) to practice languages and connect with peers worldwide. The platform combines Instagram-like social features with language exchange functionality, group conversations, and secure messaging.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **Styling**: Tailwind CSS with shadcn/ui component library
- **UI Style**: "New York" variant from shadcn/ui with custom gradient themes
- **State Management**: TanStack Query (React Query) for server state
- **Routing**: Wouter for lightweight client-side routing
- **Form Handling**: React Hook Form with Zod validation

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **API Pattern**: RESTful APIs with structured error handling
- **Authentication**: Replit's OpenID Connect (OIDC) integration
- **Session Management**: Express sessions with PostgreSQL storage
- **Real-time Features**: WebSocket support for live chat and group conversations

### Mobile-First Design
- Responsive design optimized for mobile devices
- Maximum width container (mobile-container) for consistent mobile experience
- Touch-friendly interface with Instagram-inspired UI patterns

## Key Components

### Authentication System
- **Provider**: Replit Auth with OIDC
- **Session Storage**: PostgreSQL-backed sessions
- **User Management**: Custom user profiles with language exchange data
- **Security**: HTTP-only cookies, secure session handling

### Data Models
- **Users**: Profile info, languages spoken/learning, interests, age verification
- **Posts**: Content sharing with images, videos, and stories (24-hour expiry)
- **Groups**: Language practice groups with voice chat capabilities
- **Messages**: Direct messaging with secure inbox features
- **Social Features**: Likes, comments, follows, notifications

### UI Components
- **Feed**: Instagram-style post feed with stories
- **Groups**: Voice chat rooms with participant management
- **Search**: User discovery by language, interests, location
- **Profile**: Comprehensive user profiles with language badges
- **Navigation**: Bottom navigation bar for mobile experience

### Language Exchange Features
- **User Matching**: Based on languages spoken vs. languages learning
- **Group Conversations**: Max 10 active speakers, unlimited listeners
- **Random Chat**: Language-specific random matching
- **Status Indicators**: Student, Working, Learning status

## Data Flow

### User Registration Flow
1. Phone number verification with country code
2. Profile creation (name, age, languages, interests)
3. Optional profile image upload
4. Status selection and bio

### Content Sharing Flow
1. Create posts with text, images, or videos
2. Optional story creation (24-hour visibility)
3. Feed distribution to followers
4. Engagement through likes and comments

### Group Communication Flow
1. Join or create language-specific groups
2. Voice chat with speaking slot management
3. Text chat for listeners
4. Admin controls for group management

### Real-time Features
- WebSocket connections for live chat
- Group voice chat coordination
- Real-time notifications
- Live status updates

## External Dependencies

### Database
- **ORM**: Drizzle ORM with PostgreSQL
- **Provider**: Neon Database (serverless PostgreSQL)
- **Migrations**: Drizzle Kit for schema management

### UI Framework
- **Component Library**: Radix UI primitives
- **Styling**: Tailwind CSS with CSS variables
- **Icons**: Lucide React icons
- **Animations**: CSS-based transitions and hover effects

### Development Tools
- **TypeScript**: Strict type checking across frontend and backend
- **ESBuild**: Production bundling for server code
- **Vite**: Development server with HMR
- **Path Aliases**: Simplified imports with @ and @shared prefixes

### Third-party Services
- **Authentication**: Replit's OIDC service
- **File Uploads**: Integration ready for image/video uploads
- **Real-time**: WebSocket server for live features

## Deployment Strategy

### Development Environment
- Vite dev server for frontend with HMR
- tsx for TypeScript execution in development
- Replit-specific plugins for development experience

### Production Build
- Vite builds static assets to dist/public
- ESBuild bundles server code to dist/index.js
- Single-file deployment with external dependencies

### Database Management
- Drizzle migrations for schema changes
- Environment-based configuration
- Connection pooling with Neon serverless

### Environment Configuration
- DATABASE_URL for PostgreSQL connection
- SESSION_SECRET for secure sessions
- REPLIT_DOMAINS for authentication
- NODE_ENV for environment-specific behavior

The architecture prioritizes mobile-first user experience, real-time communication, and secure language exchange features while maintaining scalability and developer experience.