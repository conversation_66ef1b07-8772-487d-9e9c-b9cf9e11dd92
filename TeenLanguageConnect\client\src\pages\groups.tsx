import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import TopBar from "@/components/layout/top-bar";
import BottomNav from "@/components/layout/bottom-nav";
import GroupCard from "@/components/groups/group-card";
import RandomChat from "@/components/groups/random-chat";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Plus } from "lucide-react";
import { useLocation } from "wouter";

export default function Groups() {
  const [, setLocation] = useLocation();
  const [filter, setFilter] = useState<"all" | "active" | "my-groups">("all");

  const { data: groups, isLoading: groupsLoading } = useQuery({
    queryKey: ["/api/groups", { active: filter === "active" }],
  });

  const handleBack = () => {
    setLocation("/");
  };

  const activeGroups = groups?.filter((group: any) => group.isActive) || [];
  const inactiveGroups = groups?.filter((group: any) => !group.isActive) || [];

  return (
    <div className="mobile-container">
      {/* Groups Header */}
      <header className="sticky top-0 bg-card border-b border-border px-4 py-3 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="p-2 hover:bg-muted rounded-full"
            >
              <ArrowLeft className="w-5 h-5 text-muted-foreground" />
            </Button>
            <h2 className="text-lg font-semibold">Language Groups</h2>
          </div>
          <Button className="gradient-primary text-white px-4 py-2 rounded-full text-sm font-medium btn-hover-scale">
            <Plus className="w-4 h-4 mr-1" />
            Create Group
          </Button>
        </div>
      </header>

      <main className="pb-20">
        {/* Filter Tabs */}
        <div className="px-4 py-3 border-b border-border">
          <div className="flex space-x-1 bg-muted rounded-lg p-1">
            <Button
              variant={filter === "all" ? "default" : "ghost"}
              size="sm"
              onClick={() => setFilter("all")}
              className={`flex-1 text-sm ${filter === "all" ? "bg-card shadow-sm" : ""}`}
            >
              All Groups
            </Button>
            <Button
              variant={filter === "active" ? "default" : "ghost"}
              size="sm"
              onClick={() => setFilter("active")}
              className={`flex-1 text-sm ${filter === "active" ? "bg-card shadow-sm" : ""}`}
            >
              Active Now
            </Button>
            <Button
              variant={filter === "my-groups" ? "default" : "ghost"}
              size="sm"
              onClick={() => setFilter("my-groups")}
              className={`flex-1 text-sm ${filter === "my-groups" ? "bg-card shadow-sm" : ""}`}
            >
              My Groups
            </Button>
          </div>
        </div>

        <div className="p-4 space-y-6">
          {/* Random Chat Section */}
          <RandomChat />

          {/* Active Groups */}
          {activeGroups.length > 0 && (
            <div>
              <h3 className="font-semibold text-card-foreground mb-3 flex items-center">
                <span className="w-3 h-3 bg-success rounded-full mr-2 animate-pulse-color"></span>
                Active Now ({activeGroups.length})
              </h3>
              <div className="space-y-3">
                {activeGroups.map((group: any) => (
                  <GroupCard key={group.id} group={group} />
                ))}
              </div>
            </div>
          )}

          {/* Recommended Groups */}
          <div>
            <h3 className="font-semibold text-card-foreground mb-3">
              Recommended for You
            </h3>

            {groupsLoading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="bg-card rounded-xl p-4 border border-border">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-muted rounded-lg animate-pulse"></div>
                        <div className="space-y-2">
                          <div className="w-32 h-4 bg-muted rounded animate-pulse"></div>
                          <div className="w-24 h-3 bg-muted rounded animate-pulse"></div>
                        </div>
                      </div>
                      <div className="w-16 h-8 bg-muted rounded-full animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : inactiveGroups.length > 0 ? (
              <div className="space-y-3">
                {inactiveGroups.map((group: any) => (
                  <GroupCard key={group.id} group={group} />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-muted rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Plus className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold text-card-foreground mb-2">
                  No groups yet
                </h3>
                <p className="text-muted-foreground mb-4">
                  Be the first to create a language learning group
                </p>
                <Button className="gradient-primary text-white">
                  Create First Group
                </Button>
              </div>
            )}
          </div>
        </div>
      </main>

      <BottomNav />
    </div>
  );
}
