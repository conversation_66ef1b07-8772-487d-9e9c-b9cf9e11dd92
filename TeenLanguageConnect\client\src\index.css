@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(250, 77%, 65%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(265, 84%, 68%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(187, 94%, 43%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --success: hsl(153, 73%, 47%);
  --warning: hsl(39, 95%, 48%);
  --error: hsl(4, 86%, 58%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(250, 77%, 65%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --success: hsl(153, 73%, 47%);
  --warning: hsl(39, 95%, 48%);
  --error: hsl(4, 86%, 58%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Custom styles for the app */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, hsl(250, 77%, 65%) 0%, hsl(265, 84%, 68%) 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, hsl(187, 94%, 43%) 0%, hsl(153, 73%, 47%) 100%);
}

/* Animation for pulse effect */
@keyframes pulse-color {
  0%, 100% { 
    background-color: hsl(153, 73%, 47%);
  }
  50% { 
    background-color: hsl(153, 73%, 60%);
  }
}

.animate-pulse-color {
  animation: pulse-color 2s infinite;
}

/* Custom button hover effects */
.btn-hover-scale {
  transition: all 0.2s ease-in-out;
}

.btn-hover-scale:hover {
  transform: scale(1.05);
}

/* Instagram-like grid */
.posts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1px;
}

.post-grid-item {
  aspect-ratio: 1;
  overflow: hidden;
  position: relative;
}

/* Live indicator animation */
.live-indicator {
  position: relative;
}

.live-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: hsl(153, 73%, 47%);
  animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* Story ring gradient */
.story-ring {
  background: linear-gradient(45deg, hsl(250, 77%, 65%), hsl(265, 84%, 68%), hsl(187, 94%, 43%), hsl(153, 73%, 47%));
  padding: 2px;
  border-radius: 50%;
}

.story-ring-inner {
  background: var(--background);
  border-radius: 50%;
  padding: 2px;
}

/* Mobile-first responsive container */
.mobile-container {
  max-width: 428px;
  margin: 0 auto;
  min-height: 100vh;
  position: relative;
  background: var(--background);
}

/* Bottom navigation shadow */
.bottom-nav {
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for messages */
.messages-scroll {
  scrollbar-width: thin;
  scrollbar-color: hsl(240, 3.7%, 15.9%) transparent;
}

.messages-scroll::-webkit-scrollbar {
  width: 4px;
}

.messages-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.messages-scroll::-webkit-scrollbar-thumb {
  background-color: hsl(240, 3.7%, 15.9%);
  border-radius: 2px;
}

/* Notification badge */
.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 20px;
  height: 20px;
  background: hsl(4, 86%, 58%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

/* Language badge styles */
.language-badge-native {
  background: hsl(220, 91%, 95%);
  color: hsl(220, 91%, 35%);
  border: 1px solid hsl(220, 91%, 85%);
}

.language-badge-learning {
  background: hsl(265, 91%, 95%);
  color: hsl(265, 91%, 35%);
  border: 1px solid hsl(265, 91%, 85%);
}

.language-badge-interested {
  background: hsl(153, 91%, 95%);
  color: hsl(153, 91%, 35%);
  border: 1px solid hsl(153, 91%, 85%);
}

.dark .language-badge-native {
  background: hsl(220, 91%, 15%);
  color: hsl(220, 91%, 75%);
  border: 1px solid hsl(220, 91%, 25%);
}

.dark .language-badge-learning {
  background: hsl(265, 91%, 15%);
  color: hsl(265, 91%, 75%);
  border: 1px solid hsl(265, 91%, 25%);
}

.dark .language-badge-interested {
  background: hsl(153, 91%, 15%);
  color: hsl(153, 91%, 75%);
  border: 1px solid hsl(153, 91%, 25%);
}

/* Group status indicators */
.group-active {
  background: linear-gradient(135deg, hsl(250, 77%, 65%) 0%, hsl(265, 84%, 68%) 20%);
  border: 1px solid hsl(250, 77%, 75%);
}

.group-spanish {
  background: linear-gradient(135deg, hsl(187, 94%, 43%) 0%, hsl(153, 73%, 47%) 20%);
  border: 1px solid hsl(187, 94%, 53%);
}

.group-inactive {
  background: var(--card);
  border: 1px solid var(--border);
}

/* Modal backdrop */
.modal-backdrop {
  backdrop-filter: blur(4px);
  background: rgba(0, 0, 0, 0.5);
}

/* Smooth page transitions */
.page-transition {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.page-enter {
  opacity: 0;
  transform: translateX(100%);
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
}

.page-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateX(-100%);
}
