import { useQuery } from "@tanstack/react-query";
import TopBar from "@/components/layout/top-bar";
import BottomNav from "@/components/layout/bottom-nav";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Heart, MessageSquare, UserPlus, Users } from "lucide-react";
import { useLocation } from "wouter";

export default function Notifications() {
  const [, setLocation] = useLocation();

  const handleBack = () => {
    setLocation("/");
  };

  // Mock notifications data - replace with real API call
  const notifications = [
    {
      id: 1,
      type: "like",
      user: { name: "<PERSON>", profileImage: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&w=40&h=40&fit=crop&crop=face" },
      message: "liked your post",
      time: "2 minutes ago",
      unread: true,
    },
    {
      id: 2,
      type: "follow",
      user: { name: "<PERSON>", profileImage: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&w=40&h=40&fit=crop&crop=face" },
      message: "started following you",
      time: "1 hour ago",
      unread: true,
    },
    {
      id: 3,
      type: "comment",
      user: { name: "Sophie", profileImage: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&w=40&h=40&fit=crop&crop=face" },
      message: "commented on your post",
      time: "3 hours ago",
      unread: false,
    },
    {
      id: 4,
      type: "group_invite",
      user: { name: "Alex", profileImage: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&w=40&h=40&fit=crop&crop=face" },
      message: "invited you to join Japanese Conversation Circle",
      time: "5 hours ago",
      unread: false,
    },
  ];

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "like":
        return <Heart className="w-5 h-5 text-red-500" fill="currentColor" />;
      case "follow":
        return <UserPlus className="w-5 h-5 text-primary" />;
      case "comment":
        return <MessageSquare className="w-5 h-5 text-blue-500" />;
      case "group_invite":
        return <Users className="w-5 h-5 text-green-500" />;
      default:
        return <Heart className="w-5 h-5 text-muted-foreground" />;
    }
  };

  return (
    <div className="mobile-container">
      {/* Notifications Header */}
      <header className="sticky top-0 bg-card border-b border-border px-4 py-3 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="p-2 hover:bg-muted rounded-full"
            >
              <ArrowLeft className="w-5 h-5 text-muted-foreground" />
            </Button>
            <h2 className="text-lg font-semibold">Notifications</h2>
          </div>
          <Button variant="ghost" size="sm" className="text-primary">
            Mark all read
          </Button>
        </div>
      </header>

      <main className="pb-20">
        {notifications.length > 0 ? (
          <div className="divide-y divide-border">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 hover:bg-muted/50 cursor-pointer ${
                  notification.unread ? "bg-primary/5" : ""
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className="relative">
                    <img
                      src={notification.user.profileImage}
                      alt={notification.user.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div className="absolute -bottom-1 -right-1 bg-card rounded-full p-1">
                      {getNotificationIcon(notification.type)}
                    </div>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm">
                          <span className="font-semibold text-card-foreground">
                            {notification.user.name}
                          </span>{" "}
                          <span className="text-muted-foreground">
                            {notification.message}
                          </span>
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {notification.time}
                        </p>
                      </div>
                      {notification.unread && (
                        <div className="w-2 h-2 bg-primary rounded-full ml-2 mt-1"></div>
                      )}
                    </div>

                    {/* Action buttons for specific notification types */}
                    {notification.type === "follow" && (
                      <div className="mt-2">
                        <Button size="sm" variant="outline" className="text-xs">
                          Follow Back
                        </Button>
                      </div>
                    )}

                    {notification.type === "group_invite" && (
                      <div className="mt-2 flex space-x-2">
                        <Button size="sm" className="text-xs gradient-primary text-white">
                          Accept
                        </Button>
                        <Button size="sm" variant="outline" className="text-xs">
                          Decline
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-muted rounded-full mx-auto mb-4 flex items-center justify-center">
              <Heart className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold text-card-foreground mb-2">
              No notifications yet
            </h3>
            <p className="text-muted-foreground">
              When others interact with you, you'll see it here
            </p>
          </div>
        )}
      </main>

      <BottomNav />
    </div>
  );
}
