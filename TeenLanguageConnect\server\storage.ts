import { 
  users, 
  posts,
  likes,
  comments,
  follows,
  groups,
  groupMembers,
  messages,
  reports,
  randomChats,
  type User, 
  type UpsertUser,
  type Post,
  type CreatePost,
  type Group,
  type CreateGroup,
  type Message,
  type CreateMessage,
  type Comment,
  type CreateComment,
  type Report,
  type CreateReport,
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, or, like, count, sql } from "drizzle-orm";

// Interface for storage operations
export interface IStorage {
  // User operations
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  getUserProfile(id: string): Promise<User | undefined>;
  updateUserProfile(id: string, data: Partial<UpsertUser>): Promise<User>;
  searchUsers(filters: {
    query?: string;
    languages?: string[];
    age?: number;
  }): Promise<User[]>;
  getAllUsers(pagination: { page: number; limit: number }): Promise<User[]>;
  blockUser(id: string): Promise<void>;
  
  // Follow operations
  followUser(followerId: string, followingId: string): Promise<void>;
  unfollowUser(followerId: string, followingId: string): Promise<void>;
  
  // Post operations
  getFeedPosts(userId: string, pagination: { page: number; limit: number }): Promise<any[]>;
  getExplorePosts(pagination: { page: number; limit: number }): Promise<Post[]>;
  getStories(userId: string): Promise<any[]>;
  createPost(post: CreatePost): Promise<Post>;
  likePost(userId: string, postId: number): Promise<void>;
  unlikePost(userId: string, postId: number): Promise<void>;
  getPostComments(postId: number, pagination: { page: number; limit: number }): Promise<any[]>;
  createComment(comment: CreateComment): Promise<Comment>;
  
  // Group operations
  getGroups(filters: {
    language?: string;
    level?: string;
    active?: boolean;
  }): Promise<any[]>;
  createGroup(group: CreateGroup): Promise<Group>;
  joinGroup(groupId: number, userId: string, role?: string): Promise<void>;
  leaveGroup(groupId: number, userId: string): Promise<void>;
  getGroupMessages(groupId: number, pagination: { page: number; limit: number }): Promise<any[]>;
  createMessage(message: CreateMessage): Promise<Message>;
  
  // Random chat operations
  startRandomChat(userId: string, language?: string): Promise<any>;
  endRandomChat(chatId: number): Promise<void>;
  
  // Report operations
  createReport(report: CreateReport): Promise<Report>;
  getReports(filters: {
    status?: string;
    page: number;
    limit: number;
  }): Promise<Report[]>;
  
  // Statistics
  getStats(): Promise<any>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(userData)
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  async getUserProfile(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async updateUserProfile(id: string, data: Partial<UpsertUser>): Promise<User> {
    const [user] = await db
      .update(users)
      .set({ ...data, updatedAt: new Date() })
      .where(eq(users.id, id))
      .returning();
    return user;
  }

  async searchUsers(filters: {
    query?: string;
    languages?: string[];
    age?: number;
  }): Promise<User[]> {
    let query = db.select().from(users);

    if (filters.query) {
      query = query.where(
        or(
          like(users.username, `%${filters.query}%`),
          like(users.firstName, `%${filters.query}%`),
          like(users.lastName, `%${filters.query}%`)
        )
      );
    }

    const result = await query.limit(20);
    return result;
  }

  async getAllUsers(pagination: { page: number; limit: number }): Promise<User[]> {
    const offset = (pagination.page - 1) * pagination.limit;
    const result = await db
      .select()
      .from(users)
      .orderBy(desc(users.createdAt))
      .limit(pagination.limit)
      .offset(offset);
    return result;
  }

  async blockUser(id: string): Promise<void> {
    await db.update(users).set({ isBlocked: true }).where(eq(users.id, id));
  }

  // Follow operations
  async followUser(followerId: string, followingId: string): Promise<void> {
    await db.insert(follows).values({ followerId, followingId });
  }

  async unfollowUser(followerId: string, followingId: string): Promise<void> {
    await db.delete(follows).where(
      and(
        eq(follows.followerId, followerId),
        eq(follows.followingId, followingId)
      )
    );
  }

  // Post operations
  async getFeedPosts(userId: string, pagination: { page: number; limit: number }): Promise<any[]> {
    const offset = (pagination.page - 1) * pagination.limit;
    
    const result = await db
      .select({
        id: posts.id,
        content: posts.content,
        imageUrl: posts.imageUrl,
        videoUrl: posts.videoUrl,
        likesCount: posts.likesCount,
        commentsCount: posts.commentsCount,
        createdAt: posts.createdAt,
        user: {
          id: users.id,
          username: users.username,
          firstName: users.firstName,
          lastName: users.lastName,
          profileImageUrl: users.profileImageUrl,
          country: users.country,
          languagesLearning: users.languagesLearning,
        },
      })
      .from(posts)
      .leftJoin(users, eq(posts.userId, users.id))
      .where(eq(posts.isStory, false))
      .orderBy(desc(posts.createdAt))
      .limit(pagination.limit)
      .offset(offset);

    return result;
  }

  async getExplorePosts(pagination: { page: number; limit: number }): Promise<Post[]> {
    const offset = (pagination.page - 1) * pagination.limit;
    const result = await db
      .select()
      .from(posts)
      .where(and(eq(posts.isStory, false), sql`${posts.imageUrl} IS NOT NULL`))
      .orderBy(sql`RANDOM()`)
      .limit(pagination.limit)
      .offset(offset);
    return result;
  }

  async getStories(userId: string): Promise<any[]> {
    const result = await db
      .select({
        id: posts.id,
        userId: posts.userId,
        imageUrl: posts.imageUrl,
        createdAt: posts.createdAt,
        user: {
          username: users.username,
          firstName: users.firstName,
          lastName: users.lastName,
          profileImageUrl: users.profileImageUrl,
        },
      })
      .from(posts)
      .leftJoin(users, eq(posts.userId, users.id))
      .where(
        and(
          eq(posts.isStory, true),
          sql`${posts.storyExpiresAt} > NOW()`
        )
      )
      .orderBy(desc(posts.createdAt))
      .limit(20);

    return result;
  }

  async createPost(post: CreatePost): Promise<Post> {
    const [newPost] = await db.insert(posts).values(post).returning();
    return newPost;
  }

  async likePost(userId: string, postId: number): Promise<void> {
    await db.insert(likes).values({ userId, postId });
    await db
      .update(posts)
      .set({ likesCount: sql`${posts.likesCount} + 1` })
      .where(eq(posts.id, postId));
  }

  async unlikePost(userId: string, postId: number): Promise<void> {
    await db.delete(likes).where(
      and(eq(likes.userId, userId), eq(likes.postId, postId))
    );
    await db
      .update(posts)
      .set({ likesCount: sql`${posts.likesCount} - 1` })
      .where(eq(posts.id, postId));
  }

  async getPostComments(postId: number, pagination: { page: number; limit: number }): Promise<any[]> {
    const offset = (pagination.page - 1) * pagination.limit;
    
    const result = await db
      .select({
        id: comments.id,
        content: comments.content,
        createdAt: comments.createdAt,
        user: {
          id: users.id,
          username: users.username,
          firstName: users.firstName,
          lastName: users.lastName,
          profileImageUrl: users.profileImageUrl,
        },
      })
      .from(comments)
      .leftJoin(users, eq(comments.userId, users.id))
      .where(eq(comments.postId, postId))
      .orderBy(desc(comments.createdAt))
      .limit(pagination.limit)
      .offset(offset);

    return result;
  }

  async createComment(comment: CreateComment): Promise<Comment> {
    const [newComment] = await db.insert(comments).values(comment).returning();
    await db
      .update(posts)
      .set({ commentsCount: sql`${posts.commentsCount} + 1` })
      .where(eq(posts.id, comment.postId));
    return newComment;
  }

  // Group operations
  async getGroups(filters: {
    language?: string;
    level?: string;
    active?: boolean;
  }): Promise<any[]> {
    let query = db
      .select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        language: groups.language,
        level: groups.level,
        maxParticipants: groups.maxParticipants,
        currentParticipants: groups.currentParticipants,
        isActive: groups.isActive,
        isPrivate: groups.isPrivate,
        createdAt: groups.createdAt,
        admin: {
          firstName: users.firstName,
          lastName: users.lastName,
          profileImageUrl: users.profileImageUrl,
        },
      })
      .from(groups)
      .leftJoin(users, eq(groups.adminId, users.id));

    if (filters.language) {
      query = query.where(eq(groups.language, filters.language));
    }
    if (filters.level) {
      query = query.where(eq(groups.level, filters.level));
    }
    if (filters.active !== undefined) {
      query = query.where(eq(groups.isActive, filters.active));
    }

    const result = await query.orderBy(desc(groups.isActive), desc(groups.createdAt));
    return result;
  }

  async createGroup(group: CreateGroup): Promise<Group> {
    const [newGroup] = await db.insert(groups).values(group).returning();
    return newGroup;
  }

  async joinGroup(groupId: number, userId: string, role: string = 'member'): Promise<void> {
    await db.insert(groupMembers).values({ groupId, userId, role });
    await db
      .update(groups)
      .set({ currentParticipants: sql`${groups.currentParticipants} + 1` })
      .where(eq(groups.id, groupId));
  }

  async leaveGroup(groupId: number, userId: string): Promise<void> {
    await db.delete(groupMembers).where(
      and(eq(groupMembers.groupId, groupId), eq(groupMembers.userId, userId))
    );
    await db
      .update(groups)
      .set({ currentParticipants: sql`${groups.currentParticipants} - 1` })
      .where(eq(groups.id, groupId));
  }

  async getGroupMessages(groupId: number, pagination: { page: number; limit: number }): Promise<any[]> {
    const offset = (pagination.page - 1) * pagination.limit;
    
    const result = await db
      .select({
        id: messages.id,
        content: messages.content,
        messageType: messages.messageType,
        isOneTime: messages.isOneTime,
        createdAt: messages.createdAt,
        user: {
          id: users.id,
          username: users.username,
          firstName: users.firstName,
          lastName: users.lastName,
          profileImageUrl: users.profileImageUrl,
        },
      })
      .from(messages)
      .leftJoin(users, eq(messages.userId, users.id))
      .where(and(eq(messages.groupId, groupId), eq(messages.isDeleted, false)))
      .orderBy(desc(messages.createdAt))
      .limit(pagination.limit)
      .offset(offset);

    return result;
  }

  async createMessage(message: CreateMessage): Promise<Message> {
    const [newMessage] = await db.insert(messages).values(message).returning();
    return newMessage;
  }

  // Random chat operations
  async startRandomChat(userId: string, language?: string): Promise<any> {
    // Simple implementation - in real app would have better matching logic
    const [chat] = await db.insert(randomChats).values({
      user1Id: userId,
      user2Id: "mock-user-id", // In real implementation, find available user
      language,
    }).returning();
    return chat;
  }

  async endRandomChat(chatId: number): Promise<void> {
    await db
      .update(randomChats)
      .set({ status: "ended", endedAt: new Date() })
      .where(eq(randomChats.id, chatId));
  }

  // Report operations
  async createReport(report: CreateReport): Promise<Report> {
    const [newReport] = await db.insert(reports).values(report).returning();
    return newReport;
  }

  async getReports(filters: {
    status?: string;
    page: number;
    limit: number;
  }): Promise<Report[]> {
    const offset = (filters.page - 1) * filters.limit;
    let query = db.select().from(reports);

    if (filters.status) {
      query = query.where(eq(reports.status, filters.status));
    }

    const result = await query
      .orderBy(desc(reports.createdAt))
      .limit(filters.limit)
      .offset(offset);

    return result;
  }

  // Statistics
  async getStats(): Promise<any> {
    const [totalUsers] = await db.select({ count: count() }).from(users);
    const [totalPosts] = await db.select({ count: count() }).from(posts);
    const [totalGroups] = await db.select({ count: count() }).from(groups);
    const [activeGroups] = await db
      .select({ count: count() })
      .from(groups)
      .where(eq(groups.isActive, true));

    return {
      totalUsers: totalUsers.count,
      totalPosts: totalPosts.count,
      totalGroups: totalGroups.count,
      activeGroups: activeGroups.count,
    };
  }
}

export const storage = new DatabaseStorage();
