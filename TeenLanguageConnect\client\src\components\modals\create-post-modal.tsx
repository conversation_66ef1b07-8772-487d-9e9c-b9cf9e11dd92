import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Camera, Image, Video, Clock } from "lucide-react";
import { z } from "zod";

const createPostSchema = z.object({
  content: z.string().optional(),
  imageUrl: z.string().optional(),
  videoUrl: z.string().optional(),
  isStory: z.boolean().default(false),
}).refine(
  (data) => data.content || data.imageUrl || data.videoUrl,
  {
    message: "Please add content, image, or video",
    path: ["content"],
  }
);

type CreatePostForm = z.infer<typeof createPostSchema>;

interface CreatePostModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function CreatePostModal({ open, onOpenChange }: CreatePostModalProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [postType, setPostType] = useState<"post" | "story">("post");

  const form = useForm<CreatePostForm>({
    resolver: zodResolver(createPostSchema),
    defaultValues: {
      content: "",
      isStory: false,
    },
  });

  const createPostMutation = useMutation({
    mutationFn: async (data: CreatePostForm) => {
      // In a real app, you would upload the file first and get the URL
      let imageUrl = data.imageUrl;
      if (uploadedFile && uploadedFile.type.startsWith('image/')) {
        // Mock image URL - in real app would upload to cloud storage
        imageUrl = `https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&w=800&h=600&fit=crop`;
      }

      const postData = {
        ...data,
        imageUrl,
        isStory: postType === "story",
        storyExpiresAt: postType === "story" ? new Date(Date.now() + 24 * 60 * 60 * 1000) : undefined,
      };

      return await apiRequest("POST", "/api/posts", postData);
    },
    onSuccess: () => {
      toast({
        title: postType === "story" ? "Story Posted!" : "Post Created!",
        description: postType === "story" 
          ? "Your story is now visible to followers for 24 hours"
          : "Your post has been shared with your followers",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/posts/feed"] });
      queryClient.invalidateQueries({ queryKey: ["/api/posts/stories"] });
      onOpenChange(false);
      form.reset();
      setUploadedFile(null);
      setPreviewUrl(null);
      setPostType("post");
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create post. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: CreatePostForm) => {
    createPostMutation.mutate(data);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      
      // Set the imageUrl in the form
      if (file.type.startsWith('image/')) {
        form.setValue('imageUrl', url);
      } else if (file.type.startsWith('video/')) {
        form.setValue('videoUrl', url);
      }
    }
  };

  const removeFile = () => {
    setUploadedFile(null);
    setPreviewUrl(null);
    form.setValue('imageUrl', '');
    form.setValue('videoUrl', '');
  };

  const handleClose = () => {
    onOpenChange(false);
    form.reset();
    setUploadedFile(null);
    setPreviewUrl(null);
    setPostType("post");
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>{postType === "story" ? "Add Story" : "Create Post"}</span>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Story</span>
              <Switch
                checked={postType === "story"}
                onCheckedChange={(checked) => setPostType(checked ? "story" : "post")}
              />
            </div>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            
            {/* User Header */}
            <div className="flex items-center space-x-3">
              <img
                src={user?.profileImageUrl || `https://ui-avatars.com/api/?name=${user?.firstName}+${user?.lastName}&background=6366f1&color=fff`}
                alt="Your profile"
                className="w-10 h-10 rounded-full object-cover"
              />
              <div>
                <p className="font-semibold text-sm">
                  {user?.username || `${user?.firstName} ${user?.lastName}`}
                </p>
                {postType === "story" && (
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Clock className="w-3 h-3 mr-1" />
                    Visible for 24 hours
                  </div>
                )}
              </div>
            </div>

            {/* Content Field */}
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder={postType === "story" 
                        ? "Share what you're learning today..." 
                        : "What's on your mind? Share your language learning journey..."
                      }
                      rows={4}
                      className="resize-none border-0 bg-transparent text-base focus-visible:ring-0 p-0"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* File Preview */}
            {previewUrl && (
              <div className="relative rounded-lg overflow-hidden bg-muted">
                {uploadedFile?.type.startsWith('image/') ? (
                  <img src={previewUrl} alt="Preview" className="w-full h-64 object-cover" />
                ) : (
                  <video src={previewUrl} className="w-full h-64 object-cover" controls />
                )}
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  onClick={removeFile}
                  className="absolute top-2 right-2"
                >
                  Remove
                </Button>
              </div>
            )}

            {/* Upload Options */}
            {!previewUrl && (
              <div className="grid grid-cols-2 gap-3">
                <label className="flex flex-col items-center justify-center p-4 border border-dashed border-border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
                  <Image className="w-8 h-8 text-muted-foreground mb-2" />
                  <span className="text-sm text-muted-foreground">Add Photo</span>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </label>
                
                <label className="flex flex-col items-center justify-center p-4 border border-dashed border-border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
                  <Video className="w-8 h-8 text-muted-foreground mb-2" />
                  <span className="text-sm text-muted-foreground">Add Video</span>
                  <input
                    type="file"
                    accept="video/*"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </label>
              </div>
            )}

            {/* URL Input Fields */}
            <div className="space-y-3">
              <FormField
                control={form.control}
                name="imageUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="Or paste image URL..."
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          if (e.target.value && !uploadedFile) {
                            setPreviewUrl(e.target.value);
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="videoUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="Or paste video URL..."
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          if (e.target.value && !uploadedFile) {
                            setPreviewUrl(e.target.value);
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleClose}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                className="flex-1 gradient-primary text-white"
                disabled={createPostMutation.isPending}
              >
                {createPostMutation.isPending 
                  ? "Posting..." 
                  : postType === "story" 
                    ? "Share Story" 
                    : "Share Post"
                }
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
