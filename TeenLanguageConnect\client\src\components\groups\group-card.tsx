import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Mic, Users, Clock } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface GroupProps {
  group: {
    id: number;
    name: string;
    description?: string;
    language?: string;
    level?: string;
    maxParticipants: number;
    currentParticipants: number;
    isActive: boolean;
    admin: {
      firstName: string;
      lastName: string;
      profileImageUrl?: string;
    };
    members?: Array<{
      user: {
        firstName: string;
        lastName: string;
        profileImageUrl?: string;
      };
    }>;
  };
}

export default function GroupCard({ group }: GroupProps) {
  const [isJoining, setIsJoining] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const joinMutation = useMutation({
    mutationFn: async () => {
      await apiRequest("POST", `/api/groups/${group.id}/join`);
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: `Joined ${group.name} successfully!`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/groups"] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to join group",
        variant: "destructive",
      });
    },
  });

  const handleJoin = () => {
    if (group.isActive) {
      // For active groups, join the conversation immediately
      setIsJoining(true);
      // TODO: Implement WebSocket connection for voice chat
      toast({
        title: "Joining Conversation",
        description: "Connecting to voice chat...",
      });
    } else {
      // For inactive groups, join as member
      joinMutation.mutate();
    }
  };

  const getGroupStyle = () => {
    if (group.isActive) {
      if (group.language?.toLowerCase().includes("japanese")) {
        return "group-active";
      } else if (group.language?.toLowerCase().includes("spanish")) {
        return "group-spanish";
      } else {
        return "group-active";
      }
    }
    return "group-inactive";
  };

  return (
    <div className={`rounded-xl p-4 mb-4 border ${getGroupStyle()}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={`rounded-lg p-2 ${
            group.isActive ? "bg-white/20 text-white" : "bg-primary text-white"
          }`}>
            {group.isActive ? (
              <Mic className="w-5 h-5" />
            ) : (
              <Users className="w-5 h-5" />
            )}
          </div>
          <div>
            <h4 className={`font-semibold ${
              group.isActive ? "text-white" : "text-card-foreground"
            }`}>
              {group.name}
            </h4>
            <p className={`text-sm ${
              group.isActive ? "text-white/80" : "text-muted-foreground"
            }`}>
              {group.level && `${group.level} • `}
              {group.currentParticipants}/{group.maxParticipants} participants
            </p>
          </div>
        </div>
        <div className="text-right">
          {group.isActive && (
            <>
              <div className="w-3 h-3 bg-success rounded-full mb-1 live-indicator"></div>
              <span className={`text-xs ${
                group.isActive ? "text-white/80" : "text-muted-foreground"
              }`}>
                Live
              </span>
            </>
          )}
        </div>
      </div>

      {/* Active Speakers/Members */}
      {group.members && group.members.length > 0 && (
        <div className="flex items-center space-x-2 mb-3">
          <div className="flex -space-x-2">
            {group.members.slice(0, 3).map((member, index) => (
              <img
                key={index}
                src={member.user.profileImageUrl || `https://ui-avatars.com/api/?name=${member.user.firstName}+${member.user.lastName}&background=6366f1&color=fff`}
                alt={`${member.user.firstName} ${member.user.lastName}`}
                className="w-8 h-8 rounded-full border-2 border-white object-cover"
              />
            ))}
          </div>
          {group.members.length > 3 && (
            <span className={`text-sm ${
              group.isActive ? "text-white/80" : "text-muted-foreground"
            }`}>
              +{group.members.length - 3} more
            </span>
          )}
        </div>
      )}

      <Button
        onClick={handleJoin}
        disabled={joinMutation.isPending || isJoining}
        className={`w-full py-2 rounded-lg font-medium transition-colors ${
          group.isActive
            ? "bg-white text-primary hover:bg-white/90"
            : "bg-primary text-white hover:bg-primary/90"
        }`}
      >
        {isJoining
          ? "Joining..."
          : group.isActive
          ? "Join Conversation"
          : "Join Group"
        }
      </Button>
    </div>
  );
}
